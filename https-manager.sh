#!/bin/bash

# HTTPS和域名管理脚本
# 用于管理Nginx、SSL证书和PM2服务

DOMAIN="rx.lout.me"
BACKEND_PORT=3000
FRONTEND_PORT=5173

show_status() {
    echo "=== 服务状态 ==="
    echo "Nginx状态: $(systemctl is-active nginx 2>/dev/null || echo '未安装')"
    echo "PM2进程:"
    pm2 list 2>/dev/null || echo "PM2未运行"
    echo ""
    echo "SSL证书状态:"
    if [ -f /etc/letsencrypt/live/$DOMAIN/fullchain.pem ]; then
        certbot certificates 2>/dev/null | grep -A 5 $DOMAIN || echo "证书信息获取失败"
    else
        echo "SSL证书未安装"
    fi
    echo ""
    echo "端口占用情况:"
    echo "端口80: $(lsof -ti:80 2>/dev/null && echo '已占用' || echo '空闲')"
    echo "端口443: $(lsof -ti:443 2>/dev/null && echo '已占用' || echo '空闲')"
    echo "端口$BACKEND_PORT: $(lsof -ti:$BACKEND_PORT 2>/dev/null && echo '已占用' || echo '空闲')"
    echo "端口$FRONTEND_PORT: $(lsof -ti:$FRONTEND_PORT 2>/dev/null && echo '已占用' || echo '空闲')"
}

restart_services() {
    echo "=== 重启服务 ==="
    echo "重启Nginx..."
    systemctl restart nginx
    echo "重启PM2应用..."
    pm2 restart all
    echo "服务重启完成"
}

renew_ssl() {
    echo "=== 续期SSL证书 ==="
    certbot renew
    systemctl reload nginx
    echo "SSL证书续期完成"
}

test_ssl() {
    echo "=== 测试SSL证书 ==="
    certbot renew --dry-run
}

check_logs() {
    echo "=== 查看日志 ==="
    echo "选择要查看的日志:"
    echo "1) Nginx访问日志"
    echo "2) Nginx错误日志"
    echo "3) PM2后端日志"
    echo "4) PM2前端日志"
    echo "5) SSL证书日志"
    read -p "请选择 (1-5): " choice
    
    case $choice in
        1) tail -f /var/log/nginx/access.log ;;
        2) tail -f /var/log/nginx/error.log ;;
        3) pm2 logs products-backend ;;
        4) pm2 logs product-showcase-dev ;;
        5) tail -f /var/log/letsencrypt/letsencrypt.log ;;
        *) echo "无效选择" ;;
    esac
}

backup_config() {
    echo "=== 备份配置 ==="
    BACKUP_DIR="/root/nginx-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p $BACKUP_DIR
    
    # 备份Nginx配置
    cp -r /etc/nginx/sites-available $BACKUP_DIR/
    cp -r /etc/nginx/sites-enabled $BACKUP_DIR/
    
    # 备份PM2配置
    cp /root/products-b-test/products-backend/ecosystem.config.js $BACKUP_DIR/
    cp /root/products-b-test/product-showcase/ecosystem.config.cjs $BACKUP_DIR/
    
    echo "配置已备份到: $BACKUP_DIR"
}

show_help() {
    echo "HTTPS和域名管理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  status      显示服务状态"
    echo "  restart     重启所有服务"
    echo "  renew       续期SSL证书"
    echo "  test        测试SSL证书续期"
    echo "  logs        查看日志"
    echo "  backup      备份配置文件"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 status   # 查看服务状态"
    echo "  $0 restart  # 重启服务"
    echo "  $0 renew    # 续期SSL证书"
}

# 主程序
case "${1:-help}" in
    status)
        show_status
        ;;
    restart)
        restart_services
        ;;
    renew)
        renew_ssl
        ;;
    test)
        test_ssl
        ;;
    logs)
        check_logs
        ;;
    backup)
        backup_config
        ;;
    help|*)
        show_help
        ;;
esac
