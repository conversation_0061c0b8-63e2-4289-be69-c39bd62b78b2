{"fields": {"name": "Product Name", "price": "Price", "category": "Category", "primaryCategory": "Primary Category", "secondaryCategory": "Secondary Category", "origin": "Origin", "country": "Country", "province": "Province", "city": "City", "platform": "Platform", "specification": "Specification", "flavor": "Flavor", "manufacturer": "Manufacturer", "collectTime": "Collection Time", "sequence": "Sequence", "link": "Link", "boxSpec": "Box Specification", "notes": "Notes"}, "labels": {"normalPrice": "Normal Price", "discountPrice": "Discount Price", "discountRate": "Discount Rate", "hasDiscount": "On Sale", "noDiscount": "Regular Price", "priceRange": "Price Range", "fromPrice": "From", "toPrice": "To"}, "images": {"front": "Front Image", "back": "Back Image", "label": "Label Image", "package": "Package Image", "gift": "Gift Image", "noImage": "No Image Available", "loadingImage": "Loading Image...", "imageError": "Failed to load image"}, "filters": {"title": "Filters", "allCategories": "All Categories", "allPlatforms": "All Platforms", "allLocations": "All Locations", "showDiscountOnly": "Show Discount Only", "priceRange": "Price Range", "filterBy": "Filter <PERSON>", "sortBy": "Sort By", "clearFilters": "Clear All Filters", "resetFilters": "Reset Filters", "applyFilters": "Apply Filters", "showDistribution": "Show Distribution", "hideDistribution": "Hide Distribution", "priceDistribution": "Price Distribution", "productCount": "{{count}} products", "showingProducts": "Showing {{filtered}} / {{total}} products", "appliedFilters": "{{count}} filters applied, showing {{filtered}} products", "filterResults": "Filter Results: {{count}} products", "minPrice": "<PERSON>", "quickSelect": "Quick Select", "customRange": "Custom Range", "maxPrice": "Max Price", "allPrices": "All", "categories": {"title": "Product Categories", "loading": "Loading...", "selected": "{{count}} categories selected", "total": "{{count}} categories total", "selectAll": "Select All", "deselectAll": "Deselect All", "noCategories": "No categories available", "selectedCategories": "Selected Categories"}, "platforms": {"title": "Collection Platforms", "loading": "Loading...", "selected": "{{count}} platforms selected", "total": "{{count}} platforms total", "selectAll": "Select All", "deselectAll": "Deselect All", "allPlatforms": "All Platforms ({{count}} products)", "noPlatforms": "No platforms available", "selectedPlatforms": "Selected Platforms", "platformStatistics": "Platform Statistics", "totalPlatforms": "Total Platforms", "largestPlatform": "Largest Platform", "itemsCount": "{{count}} items"}, "locations": {"title": "Product Origins", "loading": "Loading...", "selected": "{{count}} locations selected", "total": "{{count}} locations total", "selectAll": "Select All", "deselectAll": "Deselect All", "searchPlaceholder": "Search locations...", "popularOrigins": "Popular Origins", "selectedOrigins": "Selected Origins", "viewMode": {"list": "List View", "map": "Map View"}, "noLocations": "No locations available", "noResults": "No matching locations found", "cities": "Cities", "citiesCount": "and {{count}} more cities"}, "price": {"title": "Price Range", "loading": "Loading...", "from": "From", "to": "To", "currency": {"cny": "¥", "usd": "$"}, "distribution": "Price Distribution", "showDistribution": "Show Distribution", "hideDistribution": "Hide Distribution"}}, "sorting": {"name": "Sort by name", "priceAsc": "Price: Low to High", "priceDesc": "Price: High to Low", "collectTime": "By collection time", "latest": "Latest First", "oldest": "Oldest First"}, "actions": {"viewDetails": "View Details", "quickView": "Quick View", "favorite": "Add to Favorites", "favorited": "Favorited", "compare": "Compare", "compared": "In Comparison", "share": "Share", "copyLink": "Copy Link", "back": "Back", "backToList": "Back to Product List"}, "stats": {"totalProducts": "Total Products", "averagePrice": "Average Price", "priceRange": "Price Range", "categories": "Categories", "platforms": "Platforms", "locations": "Locations"}, "detail": {"title": "Product Detail", "pageTitle": "Product Showcase System", "breadcrumb": {"list": "Product List", "detail": "Product Detail"}, "loading": "Loading product information...", "notFound": {"title": "Product Not Found", "message": "Sorry, the product you are looking for does not exist or has been deleted", "backButton": "Back to Product List"}, "errors": {"loadFailed": "Failed to load product", "invalidId": "Invalid product ID"}, "share": {"text": "Check out this product: {{productName}}", "linkCopied": "Link copied to clipboard"}, "basicInfo": {"title": "Basic Information"}, "toast": {"favoriteAdded": "Added to favorites", "favoriteRemoved": "Removed from favorites", "compareAdded": "Added to comparison list", "compareRemoved": "Removed from comparison list", "compareLimit": "Comparison list can only contain up to 4 products"}, "defaultValues": {"noData": "N/A"}}, "info": {"sections": {"price": "Price Information", "category": "Category Information", "sales": "Sales Information", "origin": "Origin Information"}, "labels": {"normalPrice": "Regular Price", "discountPrice": "Discount Price", "discountRate": "Discount Rate", "savings": "Savings", "primaryCategory": "Primary Category", "secondaryCategory": "Secondary Category", "platform": "Platform", "manufacturer": "Manufacturer", "productLink": "Product Link", "country": "Country", "province": "Province", "city": "City", "viewLink": "View Link"}}, "related": {"title": "Related Products", "subtitle": "Based on category, price, platform and other factors, we recommend {{count}} related products for you", "noProducts": {"title": "No Related Products", "message": "No related products found for the current item"}, "reasons": {"sameCategory": "Same Category", "similarCategory": "Similar Category", "similarPrice": "Similar Price", "nearPrice": "Near Price", "samePlatform": "Same Platform", "sameOrigin": "Same Origin", "hasDiscount": "On Sale", "related": "Related"}, "algorithm": {"title": "Recommendation Algorithm", "strategies": {"category": "Same Category Recommendation", "categoryDesc": "Products from the same category", "price": "Similar Price", "priceDesc": "Products with similar price range", "platform": "Same Platform Recommendation", "platformDesc": "Products from the same platform", "origin": "Same Origin Recommendation", "originDesc": "Products from the same origin", "discount": "Discount Products", "discountDesc": "Products with discounts"}}}}