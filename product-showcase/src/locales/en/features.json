{"sync": {"title": "Data Synchronization", "status": "Sync Status", "lastSync": "Last Sync", "nextSync": "Next Sync", "inProgress": "Synchronization in Progress", "completed": "Sync Completed", "failed": "Sync Failed", "never": "Never synced", "startSync": "Start Sync", "stopSync": "Stop Sync", "autoSync": "Auto Sync", "manualSync": "Manual Sync", "syncInterval": "Sync Interval", "syncHistory": "Sync History", "totalRecords": "Total Records", "newRecords": "New Records", "updatedRecords": "Updated Records", "deletedRecords": "Deleted Records", "errorRecords": "Error Records", "syncLog": "Sync Log", "viewDetails": "View Details", "retry": "Retry"}, "performance": {"title": "Performance Monitor", "metrics": "Performance Metrics", "loadTime": "Load Time", "renderTime": "Render Time", "memoryUsage": "Memory Usage", "networkRequests": "Network Requests", "cacheHitRate": "<PERSON><PERSON> Hit Rate", "fps": "FPS", "optimization": "Optimization", "suggestions": "Performance Suggestions", "report": "Performance Report", "benchmark": "Benchmark", "profile": "Profile"}, "api": {"title": "API Demo", "endpoint": "API Endpoint", "method": "HTTP Method", "request": "Request", "response": "Response", "headers": "Headers", "parameters": "Parameters", "body": "Request Body", "status": "Status Code", "execute": "Execute Request", "clear": "Clear Response", "copy": "Copy Response", "format": "Format JSON", "documentation": "API Documentation"}}