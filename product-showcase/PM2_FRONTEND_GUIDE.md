# 前端PM2进程管理器使用指南

## 概述

前端项目已配置使用PM2进程管理器来持久化运行React + Vite应用程序。支持开发模式和预览模式两种运行方式。

## 配置文件

- **ecosystem.config.cjs**: PM2配置文件（使用.cjs扩展名适配ES模块）
- **pm2-manage.sh**: 便捷的管理脚本
- **logs/**: 日志文件目录

## 快速开始

### 1. 启动开发服务器
```bash
pm2 start ecosystem.config.cjs --only product-showcase-dev
# 或使用管理脚本
./pm2-manage.sh dev
```

### 2. 启动预览服务器（需要先构建）
```bash
npm run build
pm2 start ecosystem.config.cjs --only product-showcase-preview
# 或使用管理脚本
./pm2-manage.sh build-preview
```

### 3. 查看状态
```bash
pm2 status
# 或
./pm2-manage.sh status
```

## 服务模式说明

### 开发模式 (product-showcase-dev)
- **端口**: 5173
- **特性**: 热重载(HMR)、开发工具、源码映射
- **用途**: 开发调试
- **访问**: http://localhost:5173

### 预览模式 (product-showcase-preview)
- **端口**: 4173
- **特性**: 生产构建预览、压缩优化
- **用途**: 生产环境测试
- **访问**: http://localhost:4173
- **注意**: 需要先运行 `npm run build` 构建项目

## 管理脚本使用

项目提供了便捷的管理脚本 `pm2-manage.sh`：

```bash
# 查看帮助
./pm2-manage.sh

# 启动开发服务器
./pm2-manage.sh dev

# 启动预览服务器
./pm2-manage.sh preview

# 构建并启动预览服务器
./pm2-manage.sh build-preview

# 停止服务
./pm2-manage.sh stop-dev
./pm2-manage.sh stop-preview
./pm2-manage.sh stop-all

# 重启服务
./pm2-manage.sh restart-dev
./pm2-manage.sh restart-preview

# 查看日志
./pm2-manage.sh logs-dev
./pm2-manage.sh logs-preview

# 查看状态
./pm2-manage.sh status
```

## 常用PM2命令

### 进程管理
```bash
# 启动开发服务器
pm2 start ecosystem.config.cjs --only product-showcase-dev

# 启动预览服务器
pm2 start ecosystem.config.cjs --only product-showcase-preview

# 停止服务
pm2 stop product-showcase-dev
pm2 stop product-showcase-preview

# 重启服务
pm2 restart product-showcase-dev
pm2 restart product-showcase-preview

# 删除服务
pm2 delete product-showcase-dev
pm2 delete product-showcase-preview

# 查看状态
pm2 status
```

### 日志管理
```bash
# 查看开发服务器日志
pm2 logs product-showcase-dev

# 查看预览服务器日志
pm2 logs product-showcase-preview

# 查看最近10行日志
pm2 logs product-showcase-dev --lines 10

# 清空日志
pm2 flush
```

## 配置说明

### 开发模式配置
- **instances**: 1 - 单实例运行
- **exec_mode**: fork - Fork模式
- **max_memory_restart**: 512M - 内存限制
- **watch**: false - 关闭PM2文件监控，使用Vite的HMR

### 预览模式配置
- **instances**: 1 - 单实例运行
- **exec_mode**: fork - Fork模式
- **max_memory_restart**: 256M - 更小的内存限制
- **watch**: false - 关闭文件监控

## 开机自启动

前端服务已包含在系统的PM2自启动配置中：

```bash
# 查看自启动状态
systemctl status pm2-root

# 保存当前进程列表
pm2 save

# 恢复进程列表
pm2 resurrect
```

## 故障排除

### 1. 开发服务器无法启动
```bash
# 检查端口占用
netstat -tlnp | grep :5173

# 查看详细错误日志
pm2 logs product-showcase-dev --err

# 手动启动检查错误
npm run dev
```

### 2. 预览服务器无法启动
```bash
# 确保已构建项目
npm run build

# 检查构建输出
ls -la dist/

# 查看错误日志
pm2 logs product-showcase-preview --err
```

### 3. TypeScript编译错误
```bash
# 检查TypeScript配置
npx tsc --noEmit

# 修复类型错误后重新构建
npm run build
```

## 生产环境建议

### 静态文件服务
对于生产环境，建议使用Nginx等Web服务器提供静态文件服务：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /path/to/product-showcase/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理到后端
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 构建优化
```bash
# 生产构建
NODE_ENV=production npm run build

# 分析构建包大小
npm run build -- --analyze
```

## 监控和维护

```bash
# 实时监控
pm2 monit

# 查看进程详情
pm2 info product-showcase-dev

# 重启所有前端服务
pm2 restart all

# 查看系统资源使用
pm2 status
```

## 当前状态

✅ **前端开发服务器已启动**
- 服务名称: product-showcase-dev
- 运行端口: 5173
- 访问地址: http://localhost:5173
- 状态: 正常运行

⚠️ **预览服务器**
- 需要先解决TypeScript编译错误
- 然后运行 `npm run build` 构建项目
- 最后启动预览服务器
