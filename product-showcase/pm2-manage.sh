#!/bin/bash

# 前端PM2管理脚本
# 用法: ./pm2-manage.sh [command]

DEV_APP_NAME="product-showcase-dev"
PREVIEW_APP_NAME="product-showcase-preview"
CONFIG_FILE="ecosystem.config.cjs"

case "$1" in
    dev)
        echo "启动开发服务器..."
        pm2 start $CONFIG_FILE --only $DEV_APP_NAME
        ;;
    preview)
        echo "启动预览服务器..."
        echo "注意: 需要先运行 npm run build 构建项目"
        pm2 start $CONFIG_FILE --only $PREVIEW_APP_NAME
        ;;
    stop-dev)
        echo "停止开发服务器..."
        pm2 stop $DEV_APP_NAME
        ;;
    stop-preview)
        echo "停止预览服务器..."
        pm2 stop $PREVIEW_APP_NAME
        ;;
    stop-all)
        echo "停止所有前端服务..."
        pm2 stop $DEV_APP_NAME $PREVIEW_APP_NAME
        ;;
    restart-dev)
        echo "重启开发服务器..."
        pm2 restart $DEV_APP_NAME
        ;;
    restart-preview)
        echo "重启预览服务器..."
        pm2 restart $PREVIEW_APP_NAME
        ;;
    delete-dev)
        echo "删除开发服务器..."
        pm2 delete $DEV_APP_NAME
        ;;
    delete-preview)
        echo "删除预览服务器..."
        pm2 delete $PREVIEW_APP_NAME
        ;;
    delete-all)
        echo "删除所有前端服务..."
        pm2 delete $DEV_APP_NAME $PREVIEW_APP_NAME
        ;;
    status)
        echo "查看前端服务状态..."
        pm2 status
        ;;
    logs-dev)
        echo "查看开发服务器日志..."
        pm2 logs $DEV_APP_NAME
        ;;
    logs-preview)
        echo "查看预览服务器日志..."
        pm2 logs $PREVIEW_APP_NAME
        ;;
    monit)
        echo "打开监控界面..."
        pm2 monit
        ;;
    build-preview)
        echo "构建并启动预览服务器..."
        npm run build
        if [ $? -eq 0 ]; then
            pm2 start $CONFIG_FILE --only $PREVIEW_APP_NAME
        else
            echo "构建失败，无法启动预览服务器"
            exit 1
        fi
        ;;
    save)
        echo "保存当前PM2进程列表..."
        pm2 save
        ;;
    *)
        echo "用法: $0 {dev|preview|stop-dev|stop-preview|stop-all|restart-dev|restart-preview|delete-dev|delete-preview|delete-all|status|logs-dev|logs-preview|monit|build-preview|save}"
        echo ""
        echo "命令说明:"
        echo "  dev            - 启动开发服务器 (端口5173)"
        echo "  preview        - 启动预览服务器 (端口4173，需要先构建)"
        echo "  stop-dev       - 停止开发服务器"
        echo "  stop-preview   - 停止预览服务器"
        echo "  stop-all       - 停止所有前端服务"
        echo "  restart-dev    - 重启开发服务器"
        echo "  restart-preview- 重启预览服务器"
        echo "  delete-dev     - 删除开发服务器"
        echo "  delete-preview - 删除预览服务器"
        echo "  delete-all     - 删除所有前端服务"
        echo "  status         - 查看服务状态"
        echo "  logs-dev       - 查看开发服务器日志"
        echo "  logs-preview   - 查看预览服务器日志"
        echo "  monit          - 打开监控界面"
        echo "  build-preview  - 构建并启动预览服务器"
        echo "  save           - 保存当前PM2进程列表"
        echo ""
        echo "访问地址:"
        echo "  开发服务器: http://localhost:5173"
        echo "  预览服务器: http://localhost:4173"
        exit 1
        ;;
esac
