module.exports = {
  apps: [
    {
      name: 'product-showcase-dev',
      script: 'npm',
      args: 'run dev',
      instances: 1, // 开发模式通常只需要1个实例
      exec_mode: 'fork', // 开发模式使用fork模式
      
      // 环境变量
      env: {
        NODE_ENV: 'development',
        PORT: 5173,
        HOST: '0.0.0.0'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 4173,
        HOST: '0.0.0.0'
      },
      
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 重启策略
      autorestart: true,
      watch: false, // 开发模式下关闭PM2的文件监控，使用Vite自带的HMR
      max_memory_restart: '512M', // 前端应用内存限制较小
      
      // 进程管理
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      
      // 其他配置
      merge_logs: true,
      time: true,
      
      // 启动延迟
      wait_ready: false, // 开发服务器启动较快，不需要等待
      listen_timeout: 10000,
      
      // 关闭超时
      kill_timeout: 5000
    },
    {
      name: 'product-showcase-preview',
      script: 'npm',
      args: 'run preview',
      instances: 1,
      exec_mode: 'fork',
      
      // 环境变量
      env: {
        NODE_ENV: 'production',
        PORT: 4173,
        HOST: '0.0.0.0'
      },
      
      // 日志配置
      log_file: './logs/preview-combined.log',
      out_file: './logs/preview-out.log',
      error_file: './logs/preview-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 重启策略
      autorestart: true,
      watch: false,
      max_memory_restart: '256M',
      
      // 进程管理
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      
      // 其他配置
      merge_logs: true,
      time: true,
      
      // 启动延迟
      wait_ready: false,
      listen_timeout: 10000,
      
      // 关闭超时
      kill_timeout: 5000
    }
  ]
};
