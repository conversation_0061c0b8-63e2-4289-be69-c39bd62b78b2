const { Client: MinioClient } = require('minio');
require('dotenv').config({ path: './products-backend/.env' });

async function verifyMinIOConfig() {
  console.log('🔍 验证MinIO配置...');
  
  try {
    // 创建MinIO客户端
    const minioClient = new MinioClient({
      endPoint: process.env.MINIO_ENDPOINT || 'localhost',
      port: parseInt(process.env.MINIO_PORT) || 9000,
      useSSL: false,
      accessKey: process.env.MINIO_ACCESS_KEY || 'lcsm',
      secretKey: process.env.MINIO_SECRET_KEY || 'Sa2482047260'
    });
    
    console.log(`📡 连接到MinIO: ${process.env.MINIO_ENDPOINT}:${process.env.MINIO_PORT}`);
    console.log(`🔑 使用访问密钥: ${process.env.MINIO_ACCESS_KEY}`);
    
    // 测试连接
    const buckets = await minioClient.listBuckets();
    console.log('✅ MinIO连接成功');
    console.log(`📦 可用存储桶: ${buckets.map(b => b.name).join(', ')}`);
    
    // 检查product-images存储桶
    const bucketName = process.env.MINIO_BUCKET || 'product-images';
    const bucketExists = await minioClient.bucketExists(bucketName);
    
    if (bucketExists) {
      console.log(`✅ 存储桶 ${bucketName} 存在`);
      
      // 获取存储桶统计信息
      const objects = [];
      const stream = minioClient.listObjects(bucketName, '', true);
      
      let count = 0;
      for await (const obj of stream) {
        count++;
        if (count <= 5) {
          objects.push(obj.name);
        }
      }
      
      console.log(`📊 存储桶 ${bucketName} 包含 ${count} 个对象`);
      if (objects.length > 0) {
        console.log('📄 示例对象:');
        objects.forEach(obj => console.log(`   - ${obj}`));
      }
      
      // 测试访问权限
      try {
        const url = `http://${process.env.MINIO_ENDPOINT}:${process.env.MINIO_PORT}/${bucketName}`;
        console.log(`🌐 存储桶访问URL: ${url}`);
        console.log('✅ 配置验证完成');
      } catch (error) {
        console.log('⚠️  URL访问测试失败:', error.message);
      }
      
    } else {
      console.log(`❌ 存储桶 ${bucketName} 不存在`);
    }
    
  } catch (error) {
    console.error('❌ MinIO连接失败:', error.message);
    process.exit(1);
  }
}

// 验证环境变量
function verifyEnvVars() {
  console.log('🔧 检查环境变量配置...');
  
  const requiredVars = [
    'MINIO_ENDPOINT',
    'MINIO_PORT', 
    'MINIO_ACCESS_KEY',
    'MINIO_SECRET_KEY',
    'MINIO_BUCKET'
  ];
  
  const missing = [];
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (!value) {
      missing.push(varName);
    } else {
      console.log(`✅ ${varName}: ${varName.includes('SECRET') ? '***' : value}`);
    }
  });
  
  if (missing.length > 0) {
    console.error('❌ 缺少环境变量:', missing.join(', '));
    process.exit(1);
  }
  
  console.log('✅ 环境变量检查通过');
}

async function main() {
  console.log('🚀 开始MinIO配置验证...\n');
  
  verifyEnvVars();
  console.log('');
  
  await verifyMinIOConfig();
  
  console.log('\n🎉 所有验证通过！MinIO配置正确。');
}

main().catch(console.error);
