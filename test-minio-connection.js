require('dotenv').config({ path: './products-backend/.env' });
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

async function testMinIOConnection() {
  console.log('🔍 测试MinIO连接...');
  
  const endpoint = process.env.MINIO_ENDPOINT || 'localhost';
  const port = process.env.MINIO_PORT || '9000';
  const accessKey = process.env.MINIO_ACCESS_KEY || 'lcsm';
  const secretKey = process.env.MINIO_SECRET_KEY || 'Sa2482047260';
  const bucket = process.env.MINIO_BUCKET || 'product-images';
  
  console.log(`📡 连接信息:`);
  console.log(`   端点: ${endpoint}:${port}`);
  console.log(`   访问密钥: ${accessKey}`);
  console.log(`   存储桶: ${bucket}`);
  console.log('');
  
  try {
    // 测试MinIO服务是否运行
    console.log('🔄 测试MinIO服务状态...');
    const healthCheck = await execAsync(`curl -s -o /dev/null -w "%{http_code}" http://${endpoint}:${port}/minio/health/live`);
    
    if (healthCheck.stdout.trim() === '200') {
      console.log('✅ MinIO服务运行正常');
    } else {
      console.log('❌ MinIO服务状态异常:', healthCheck.stdout);
      return;
    }
    
    // 使用mc命令测试连接
    console.log('🔄 测试MinIO连接和认证...');
    const listResult = await execAsync(`mc ls local/${bucket} | head -5`);
    
    if (listResult.stdout) {
      console.log('✅ MinIO连接和认证成功');
      console.log('📄 存储桶内容示例:');
      console.log(listResult.stdout);
    } else {
      console.log('⚠️  存储桶为空或无法访问');
    }
    
    // 测试存储桶统计
    console.log('🔄 获取存储桶统计信息...');
    const duResult = await execAsync(`mc du local/${bucket}`);
    console.log('📊 存储桶统计:', duResult.stdout.trim());
    
    // 测试HTTP访问
    console.log('🔄 测试HTTP访问...');
    const httpTest = await execAsync(`curl -s -I http://${endpoint}:${port}/${bucket}/`);
    
    if (httpTest.stdout.includes('200 OK')) {
      console.log('✅ HTTP访问正常');
    } else {
      console.log('⚠️  HTTP访问可能有问题');
    }
    
    console.log('\n🎉 MinIO连接测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testMinIOConnection();
