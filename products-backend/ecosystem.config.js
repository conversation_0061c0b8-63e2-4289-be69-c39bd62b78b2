module.exports = {
  apps: [
    {
      name: 'products-backend',
      script: './dist/app.js',
      instances: 2, // 运行2个实例以提供负载均衡
      exec_mode: 'cluster', // 集群模式
      
      // 环境变量
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 重启策略
      autorestart: true,
      watch: false, // 生产环境建议关闭文件监控
      max_memory_restart: '1G', // 内存超过1G时重启
      
      // 进程管理
      min_uptime: '10s', // 最小运行时间
      max_restarts: 10, // 最大重启次数
      restart_delay: 4000, // 重启延迟
      
      // 其他配置
      merge_logs: true,
      time: true,
      
      // 健康检查
      health_check_grace_period: 3000,
      
      // 启动延迟
      wait_ready: true,
      listen_timeout: 10000,
      
      // 关闭超时
      kill_timeout: 5000,
      
      // 环境变量文件
      env_file: '.env'
    }
  ],
  
  // 部署配置（可选）
  deploy: {
    production: {
      user: 'root',
      host: 'localhost',
      ref: 'origin/main',
      repo: '**************:your-repo/products-backend.git',
      path: '/var/www/products-backend',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
