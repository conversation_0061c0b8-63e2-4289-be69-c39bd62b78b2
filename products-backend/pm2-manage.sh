#!/bin/bash

# PM2 管理脚本
# 用法: ./pm2-manage.sh [command]

APP_NAME="products-backend"
CONFIG_FILE="ecosystem.config.js"

case "$1" in
    start)
        echo "启动应用程序..."
        pm2 start $CONFIG_FILE
        ;;
    stop)
        echo "停止应用程序..."
        pm2 stop $APP_NAME
        ;;
    restart)
        echo "重启应用程序..."
        pm2 restart $APP_NAME
        ;;
    reload)
        echo "重新加载应用程序（零停机时间）..."
        pm2 reload $APP_NAME
        ;;
    delete)
        echo "删除应用程序..."
        pm2 delete $APP_NAME
        ;;
    status)
        echo "查看应用程序状态..."
        pm2 status
        ;;
    logs)
        echo "查看应用程序日志..."
        pm2 logs $APP_NAME
        ;;
    monit)
        echo "打开监控界面..."
        pm2 monit
        ;;
    info)
        echo "查看应用程序详细信息..."
        pm2 info $APP_NAME
        ;;
    build-start)
        echo "构建并启动应用程序..."
        npm run build
        pm2 start $CONFIG_FILE
        ;;
    build-restart)
        echo "构建并重启应用程序..."
        npm run build
        pm2 restart $APP_NAME
        ;;
    save)
        echo "保存当前PM2进程列表..."
        pm2 save
        ;;
    resurrect)
        echo "恢复保存的PM2进程列表..."
        pm2 resurrect
        ;;
    *)
        echo "用法: $0 {start|stop|restart|reload|delete|status|logs|monit|info|build-start|build-restart|save|resurrect}"
        echo ""
        echo "命令说明:"
        echo "  start        - 启动应用程序"
        echo "  stop         - 停止应用程序"
        echo "  restart      - 重启应用程序"
        echo "  reload       - 重新加载应用程序（零停机时间）"
        echo "  delete       - 删除应用程序"
        echo "  status       - 查看应用程序状态"
        echo "  logs         - 查看应用程序日志"
        echo "  monit        - 打开监控界面"
        echo "  info         - 查看应用程序详细信息"
        echo "  build-start  - 构建并启动应用程序"
        echo "  build-restart- 构建并重启应用程序"
        echo "  save         - 保存当前PM2进程列表"
        echo "  resurrect    - 恢复保存的PM2进程列表"
        exit 1
        ;;
esac
