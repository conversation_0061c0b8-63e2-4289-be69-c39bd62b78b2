const { MongoClient } = require('mongodb');
require('dotenv').config();

async function queryUnnamedProducts() {
    const client = new MongoClient(process.env.MONGODB_URI);
    
    try {
        await client.connect();
        console.log('✅ MongoDB连接成功');
        
        const db = client.db('products');
        const productsCollection = db.collection('products');
        
        // 查询所有可能的"未命名产品"变体
        const unnamedPatterns = [
            '未命名产品',
            '未命名',
            'unnamed product',
            'unnamed',
            'untitled product',
            'untitled',
            '无名产品',
            '无名称产品',
            '默认产品',
            'default product',
            'product',
            'test',
            'test product',
            '测试',
            '测试产品',
            'sample',
            'sample product',
            '样品',
            '样品产品',
            'temp',
            'temporary',
            '临时',
            '临时产品',
            ''  // 空字符串
        ];
        
        console.log('🔍 正在查询未命名产品记录...\n');

        // 首先查看产品名称的分布情况和数据类型
        const nameDistribution = await productsCollection.aggregate([
            {
                $group: {
                    _id: "$name",
                    count: { $sum: 1 },
                    type: { $first: { $type: "$name" } }
                }
            },
            {
                $sort: { count: -1 }
            },
            {
                $limit: 20
            }
        ]).toArray();

        console.log('📊 产品名称分布 (前20个):');
        nameDistribution.forEach((item, index) => {
            let displayName;
            if (item._id === null || item._id === undefined) {
                displayName = '(空值)';
            } else if (typeof item._id === 'object') {
                displayName = `[对象: ${JSON.stringify(item._id).substring(0, 50)}...]`;
            } else {
                displayName = String(item._id);
            }
            console.log(`  ${index + 1}. "${displayName}" (类型: ${item.type}): ${item.count} 个产品`);
        });
        console.log('');

        let totalUnnamedCount = 0;
        const allUnnamedProducts = [];

        // 首先查询名称字段为对象类型的产品（这些通常是无效数据）
        const objectNameProducts = await productsCollection.find({
            name: { $type: "object" }
        }).toArray();

        if (objectNameProducts.length > 0) {
            console.log(`📋 发现 ${objectNameProducts.length} 个产品的名称字段为对象类型（无效数据）:`);
            objectNameProducts.forEach((product, index) => {
                console.log(`  ${index + 1}. ID: ${product._id}`);
                console.log(`     名称对象: ${JSON.stringify(product.name)}`);
                console.log(`     分类: "${product.category || '(未设置)'}"`);
                console.log(`     价格: ${product.price || '(未设置)'}`);
                console.log(`     创建时间: ${product.createdAt || '(未设置)'}`);
                console.log('     ---');
            });
            totalUnnamedCount += objectNameProducts.length;
            allUnnamedProducts.push(...objectNameProducts);
            console.log('');
        }

        for (const pattern of unnamedPatterns) {
            const query = pattern === '' ?
                { $or: [{ name: '' }, { name: { $exists: false } }, { name: null }] } :
                { name: { $regex: new RegExp(`^${pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i') } };

            const products = await productsCollection.find(query).toArray();
            
            if (products.length > 0) {
                console.log(`📋 模式 "${pattern || '空值'}" 匹配到 ${products.length} 个产品:`);
                
                products.forEach((product, index) => {
                    console.log(`  ${index + 1}. ID: ${product._id}`);
                    console.log(`     名称: "${product.name || '(空)'}"`);
                    console.log(`     分类: "${product.category || '(未设置)'}"`);
                    console.log(`     价格: ${product.price || '(未设置)'}`);
                    console.log(`     描述: "${(product.description || '').substring(0, 50)}${(product.description || '').length > 50 ? '...' : ''}"`);
                    console.log(`     创建时间: ${product.createdAt || '(未设置)'}`);
                    console.log(`     更新时间: ${product.updatedAt || '(未设置)'}`);
                    console.log('     ---');
                });
                
                totalUnnamedCount += products.length;
                allUnnamedProducts.push(...products);
                console.log('');
            }
        }
        
        console.log(`📊 总计找到 ${totalUnnamedCount} 个未命名产品记录\n`);
        
        if (totalUnnamedCount > 0) {
            // 检查这些产品是否有关联的图片
            const imagesCollection = db.collection('images');
            const productIds = allUnnamedProducts.map(p => p._id.toString());
            
            const relatedImages = await imagesCollection.find({
                productId: { $in: productIds }
            }).toArray();
            
            console.log(`🖼️  这些未命名产品关联了 ${relatedImages.length} 张图片`);
            
            if (relatedImages.length > 0) {
                console.log('关联图片详情:');
                relatedImages.forEach((image, index) => {
                    console.log(`  ${index + 1}. 图片ID: ${image._id}`);
                    console.log(`     产品ID: ${image.productId}`);
                    console.log(`     文件名: ${image.filename || '(未设置)'}`);
                    console.log(`     URL: ${image.url || '(未设置)'}`);
                    console.log('     ---');
                });
            }
            
            // 生成删除预览
            console.log('\n⚠️  删除预览:');
            console.log(`   - 将删除 ${totalUnnamedCount} 个产品记录`);
            console.log(`   - 将删除 ${relatedImages.length} 个关联图片记录`);
            console.log('\n💾 建议在执行删除前先备份数据库');
        } else {
            console.log('✅ 未找到任何未命名产品记录');
        }
        
        return {
            unnamedProducts: allUnnamedProducts,
            relatedImages: totalUnnamedCount > 0 && relatedImages ? relatedImages : [],
            totalCount: totalUnnamedCount
        };
        
    } catch (error) {
        console.error('❌ 查询过程中发生错误:', error);
        throw error;
    } finally {
        await client.close();
        console.log('🔌 数据库连接已关闭');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    queryUnnamedProducts()
        .then(() => {
            console.log('\n✅ 查询完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('❌ 脚本执行失败:', error);
            process.exit(1);
        });
}

module.exports = { queryUnnamedProducts };
