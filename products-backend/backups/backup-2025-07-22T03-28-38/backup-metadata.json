{"timestamp": "2025-07-22T03:28:44.058Z", "database": "products", "backupPath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-22T03-28-38", "collections": [{"collection": "categories", "documentCount": 0, "filePath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-22T03-28-38/categories.json", "fileSize": 2}, {"collection": "newproducts", "documentCount": 0, "filePath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-22T03-28-38/newproducts.json", "fileSize": 2}, {"collection": "synclogs", "documentCount": 4, "filePath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-22T03-28-38/synclogs.json", "fileSize": 3665}, {"collection": "images", "documentCount": 0, "filePath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-22T03-28-38/images.json", "fileSize": 2}, {"collection": "products", "documentCount": 1209, "filePath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-22T03-28-38/products.json", "fileSize": 1395604}], "databaseStats": {"collectionCount": 5, "documentCount": 1213, "backupSize": 1399275}, "mongodbUri": "mongodb://lcs:****@152.89.168.61:27017/products?authSource=admin", "version": "1.0", "type": "pre-schema-migration", "method": "mongoose-json-export"}