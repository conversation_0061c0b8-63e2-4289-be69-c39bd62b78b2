# 环境配置
NODE_ENV=development
PORT=3000

# MongoDB 配置 - 本地实例 (备份)
# MONGODB_URI=********************************************************************

# MongoDB 配置 - 远程实例
MONGODB_URI=***********************************************************************

# MinIO 配置 - 本地实例 (备份)
# MINIO_ENDPOINT=localhost
# MINIO_PORT=9000
# MINIO_ACCESS_KEY=lcsm
# MINIO_SECRET_KEY=Sa2482047260

# MinIO 配置 - 新配置
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=lcsm
MINIO_SECRET_KEY=Sa2482047260
MINIO_BUCKET=product-images

# 飞书API配置
FEISHU_APP_ID=cli_a8fa1d87c3fad00d
FEISHU_APP_SECRET=CDfRPlOw8VRQrPyLnpzNvd5wBmu6wROp
FEISHU_APP_TOKEN=J4dFbm5S9azofMsW702cSOVwnsh
FEISHU_TABLE_ID=tblwdwrZMikMRyxq

# Redis 配置 (可选，后续添加)
# REDIS_URL=redis://host:port

# 日志配置
LOG_LEVEL=info

# 数据同步配置
SYNC_SCHEDULER_ENABLED=true
TIMEZONE=Asia/Shanghai
SYNC_BATCH_SIZE=50
SYNC_CONCURRENT_IMAGES=5
SYNC_RETRY_ATTEMPTS=3
SYNC_TIMEOUT=300000

# 外部访问配置
HOST=0.0.0.0
