# PM2 进程管理器使用指南

## 概述

本项目已配置使用PM2进程管理器来持久化运行应用程序。PM2提供了进程管理、负载均衡、日志管理、监控等功能。

## 配置文件

- **ecosystem.config.js**: PM2配置文件，包含应用程序的所有配置参数
- **pm2-manage.sh**: 便捷的管理脚本
- **logs/**: 日志文件目录

## 快速开始

### 1. 启动应用程序
```bash
pm2 start ecosystem.config.js
# 或使用管理脚本
./pm2-manage.sh start
```

### 2. 查看状态
```bash
pm2 status
# 或
./pm2-manage.sh status
```

### 3. 查看日志
```bash
pm2 logs products-backend
# 或
./pm2-manage.sh logs
```

## 常用PM2命令

### 进程管理
```bash
# 启动应用
pm2 start ecosystem.config.js

# 停止应用
pm2 stop products-backend

# 重启应用
pm2 restart products-backend

# 重新加载应用（零停机时间）
pm2 reload products-backend

# 删除应用
pm2 delete products-backend

# 查看进程状态
pm2 status

# 查看详细信息
pm2 info products-backend
```

### 日志管理
```bash
# 查看实时日志
pm2 logs products-backend

# 查看最近10行日志
pm2 logs products-backend --lines 10

# 清空日志
pm2 flush

# 重新加载日志
pm2 reloadLogs
```

### 监控
```bash
# 打开监控界面
pm2 monit

# 查看内存和CPU使用情况
pm2 status
```

## 管理脚本使用

项目提供了便捷的管理脚本 `pm2-manage.sh`：

```bash
# 查看帮助
./pm2-manage.sh

# 启动应用
./pm2-manage.sh start

# 停止应用
./pm2-manage.sh stop

# 重启应用
./pm2-manage.sh restart

# 重新加载（零停机）
./pm2-manage.sh reload

# 查看状态
./pm2-manage.sh status

# 查看日志
./pm2-manage.sh logs

# 构建并启动
./pm2-manage.sh build-start

# 构建并重启
./pm2-manage.sh build-restart
```

## 配置说明

### 主要配置参数

- **instances**: 2 - 运行2个实例提供负载均衡
- **exec_mode**: cluster - 集群模式
- **max_memory_restart**: 1G - 内存超过1G时自动重启
- **autorestart**: true - 自动重启
- **watch**: false - 生产环境建议关闭文件监控

### 日志配置

- **log_file**: ./logs/combined.log - 合并日志
- **out_file**: ./logs/out.log - 标准输出日志
- **error_file**: ./logs/error.log - 错误日志

## 开机自启动

已配置开机自启动，系统重启后PM2会自动启动应用程序。

```bash
# 查看自启动状态
systemctl status pm2-root

# 手动启用自启动（已配置）
pm2 startup

# 保存当前进程列表
pm2 save

# 恢复进程列表
pm2 resurrect
```

## 故障排除

### 1. 应用无法启动
```bash
# 查看详细错误信息
pm2 logs products-backend --err

# 检查配置文件
pm2 prettylist
```

### 2. 内存泄漏
```bash
# 查看内存使用情况
pm2 monit

# 重启应用释放内存
pm2 restart products-backend
```

### 3. 端口冲突
```bash
# 检查端口占用
netstat -tlnp | grep :3000

# 修改.env文件中的PORT配置
```

## 生产环境建议

1. **监控**: 定期检查 `pm2 monit` 和日志文件
2. **备份**: 定期备份 `/root/.pm2/dump.pm2` 文件
3. **日志轮转**: 配置日志轮转避免日志文件过大
4. **资源限制**: 根据服务器资源调整实例数量
5. **健康检查**: 定期访问 `/health` 端点检查应用状态

## 更新部署

```bash
# 1. 拉取最新代码
git pull

# 2. 安装依赖
npm install

# 3. 构建应用
npm run build

# 4. 重新加载应用（零停机）
pm2 reload products-backend

# 或使用管理脚本
./pm2-manage.sh build-restart
```
