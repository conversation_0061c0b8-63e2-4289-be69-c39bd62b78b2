#!/bin/bash

# 仅配置Nginx的脚本（不包含SSL证书获取）
# 适用于想要分步骤配置的情况

set -e

DOMAIN="rx.lout.me"
BACKEND_PORT=3000
FRONTEND_PORT=5173

echo "=== 配置Nginx反向代理 ==="

# 1. 安装Nginx
echo "1. 安装Nginx..."
apt update
apt install -y nginx

# 2. 创建基础HTTP配置（用于测试和SSL证书获取）
echo "2. 创建Nginx配置..."
cat > /etc/nginx/sites-available/$DOMAIN << 'EOF'
server {
    listen 80;
    server_name rx.lout.me;
    
    # Let's Encrypt验证路径
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    # 前端代理
    location / {
        proxy_pass http://127.0.0.1:5173;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # API后端代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF

# 3. 启用站点配置
echo "3. 启用站点配置..."
ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# 4. 测试配置
echo "4. 测试Nginx配置..."
nginx -t

# 5. 启动Nginx
echo "5. 启动Nginx..."
systemctl start nginx
systemctl enable nginx
systemctl reload nginx

echo "=== Nginx配置完成 ==="
echo "现在可以通过 http://$DOMAIN 访问您的网站"
echo ""
echo "下一步获取SSL证书，请运行："
echo "apt install -y certbot python3-certbot-nginx"
echo "certbot --nginx -d $DOMAIN --email <EMAIL> --agree-tos --non-interactive"
