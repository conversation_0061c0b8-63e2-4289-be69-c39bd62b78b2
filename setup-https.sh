#!/bin/bash

# HTTPS和域名配置脚本
# 域名: rx.lout.me
# 后端端口: 3000
# 前端端口: 5173

set -e

DOMAIN="rx.lout.me"
BACKEND_PORT=3000
FRONTEND_PORT=5173
EMAIL="<EMAIL>"  # 请替换为您的真实邮箱地址

echo "=== 开始配置HTTPS和域名访问 ==="
echo "域名: $DOMAIN"
echo "后端端口: $BACKEND_PORT"
echo "前端端口: $FRONTEND_PORT"

# 1. 更新系统包
echo "1. 更新系统包..."
apt update

# 2. 安装Nginx
echo "2. 安装Nginx..."
apt install -y nginx

# 3. 安装Certbot (Let's Encrypt)
echo "3. 安装Certbot..."
apt install -y certbot python3-certbot-nginx

# 4. 启动并启用Nginx
echo "4. 启动Nginx服务..."
systemctl start nginx
systemctl enable nginx

# 5. 创建Nginx配置文件
echo "5. 创建Nginx配置..."
cat > /etc/nginx/sites-available/$DOMAIN << 'EOF'
# HTTP配置 - 用于Let's Encrypt验证和重定向到HTTPS
server {
    listen 80;
    server_name rx.lout.me;
    
    # Let's Encrypt验证路径
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    # 其他请求重定向到HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS配置
server {
    listen 443 ssl http2;
    server_name rx.lout.me;
    
    # SSL证书配置 (将由certbot自动填充)
    # ssl_certificate /etc/letsencrypt/live/rx.lout.me/fullchain.pem;
    # ssl_certificate_key /etc/letsencrypt/live/rx.lout.me/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 前端静态文件 (根路径)
    location / {
        proxy_pass http://127.0.0.1:5173;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Vite HMR支持
        proxy_set_header Accept-Encoding gzip;
    }
    
    # API后端代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:5173;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# 6. 启用站点配置
echo "6. 启用站点配置..."
ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# 7. 测试Nginx配置
echo "7. 测试Nginx配置..."
nginx -t

# 8. 重载Nginx
echo "8. 重载Nginx..."
systemctl reload nginx

# 9. 获取SSL证书
echo "9. 获取SSL证书..."
echo "请确保域名 $DOMAIN 已正确解析到本机IP地址"
read -p "域名解析是否已完成？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    certbot --nginx -d $DOMAIN --email $EMAIL --agree-tos --non-interactive
    echo "SSL证书获取成功！"
else
    echo "请先完成域名解析，然后手动运行："
    echo "certbot --nginx -d $DOMAIN --email $EMAIL --agree-tos --non-interactive"
fi

# 10. 设置证书自动续期
echo "10. 设置证书自动续期..."
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -

# 11. 配置防火墙
echo "11. 配置防火墙..."
if command -v ufw &> /dev/null; then
    ufw allow 'Nginx Full'
    ufw allow ssh
    echo "防火墙规则已更新"
fi

echo "=== HTTPS配置完成 ==="
echo "您的网站现在可以通过以下地址访问："
echo "- HTTPS: https://$DOMAIN"
echo "- HTTP会自动重定向到HTTPS"
echo ""
echo "服务状态："
echo "- Nginx: $(systemctl is-active nginx)"
echo "- PM2后端: 运行在端口$BACKEND_PORT"
echo "- PM2前端: 运行在端口$FRONTEND_PORT"
echo ""
echo "如需查看SSL证书状态："
echo "certbot certificates"
echo ""
echo "如需手动续期证书："
echo "certbot renew"
