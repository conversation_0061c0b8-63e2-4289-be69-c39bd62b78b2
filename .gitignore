# 环境变量文件 - 包含敏感信息
.env
.env.local
.env.development
.env.production
.env.test
*.env

# 日志文件
*.log
logs/
server.log

# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建输出
dist/
build/
*.tsbuildinfo

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE 配置文件
.vscode/
.idea/
*.swp
*.swo
*~

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 缓存文件
.cache/
.parcel-cache/
.next/
.nuxt/

# 测试覆盖率
coverage/
.nyc_output/

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 可选的REPL历史
.node_repl_history

# 输出的npm包
*.tgz

# Yarn完整性文件
.yarn-integrity

# dotenv环境变量文件
.env

# 下一个.js构建输出
.next

# Nuxt.js构建/生成输出
.nuxt
dist

# Gatsby文件
.cache/
public

# Storybook构建输出
.out
.storybook-out

# 临时文件夹
.tmp
.temp

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 目录用于仪表化库生成的覆盖率报告
lib-cov

# 覆盖率目录由工具如istanbul使用
coverage

# nyc测试覆盖率
.nyc_output

# Grunt中间存储 (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower依赖目录 (https://bower.io/)
bower_components

# node-waf配置
.lock-wscript

# 编译的二进制插件 (http://nodejs.org/api/addons.html)
build/Release

# 依赖目录
# https://www.npmjs.org/doc/misc/npm-faq.html#should-i-check-my-node_modules-folder-into-git
node_modules/
jspm_packages/

# 可选的npm缓存目录
.npm

# 可选的REPL历史
.node_repl_history

# 0x
profile-*

# mac文件
.DS_Store

# vim交换文件
*.swp
*.swo

# webstorm
.idea/

# vscode
.vscode/
*.code-workspace

# 备份文件
*.bak
*.backup
*~

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件（可能包含敏感信息）
config/local.json
config/production.json
config/development.json

# 私钥文件
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer

# 证书文件
ssl/
certs/

# 密钥和凭据
secrets/
credentials/
auth/

# 本地配置
local.json
local.js
local.ts

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 图片文件（如果不需要版本控制）
# *.jpg
# *.jpeg
# *.png
# *.gif
# *.bmp
# *.tiff
# *.ico

# 视频文件
*.mp4
*.avi
*.mov
*.wmv
*.flv

# 音频文件
*.mp3
*.wav
*.flac
*.aac

# 文档文件（如果不需要版本控制）
# *.pdf
# *.doc
# *.docx
# *.xls
# *.xlsx
# *.ppt
# *.pptx

# 其他
.sass-cache/
.connect.lock
.grunt
.lock-wscript
.tern-project
.tern-port
.coverage
.nyc_output
.cache
.parcel-cache
.next
.nuxt
dist
.vuepress/dist
.serverless
.fusebox/
.dynamodb/
.tern-port
.vscode-test
