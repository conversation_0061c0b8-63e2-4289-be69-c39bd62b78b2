#!/bin/bash

# SSL证书获取和配置脚本
# 在nginx-config-only.sh执行完成后运行此脚本

set -e

DOMAIN="rx.lout.me"
EMAIL="<EMAIL>"  # 请替换为您的真实邮箱地址

echo "=== 配置SSL证书 ==="

# 1. 安装Certbot
echo "1. 安装Certbot..."
apt install -y certbot python3-certbot-nginx

# 2. 检查域名解析
echo "2. 检查域名解析..."
echo "正在检查域名 $DOMAIN 的解析情况..."
nslookup $DOMAIN || echo "域名解析检查完成"

# 3. 获取SSL证书
echo "3. 获取SSL证书..."
echo "请确保域名 $DOMAIN 已正确解析到本机IP地址"
echo "当前服务器IP地址："
curl -s ifconfig.me || curl -s ipinfo.io/ip || echo "无法获取公网IP"

read -p "确认域名解析正确？继续获取SSL证书？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    # 获取证书并自动配置Nginx
    certbot --nginx -d $DOMAIN --email $EMAIL --agree-tos --non-interactive
    
    echo "SSL证书获取成功！"
    
    # 4. 设置自动续期
    echo "4. 设置证书自动续期..."
    (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
    
    # 5. 测试自动续期
    echo "5. 测试证书续期..."
    certbot renew --dry-run
    
    echo "=== SSL配置完成 ==="
    echo "您的网站现在可以通过HTTPS访问："
    echo "https://$DOMAIN"
    echo ""
    echo "证书信息："
    certbot certificates
    
else
    echo "请先完成域名解析，然后重新运行此脚本"
    echo "或手动运行："
    echo "certbot --nginx -d $DOMAIN --email $EMAIL --agree-tos --non-interactive"
fi

# 6. 配置防火墙（如果存在）
echo "6. 配置防火墙..."
if command -v ufw &> /dev/null; then
    ufw allow 'Nginx Full'
    ufw allow ssh
    echo "防火墙规则已更新"
else
    echo "未检测到ufw防火墙，请手动确保端口80和443已开放"
fi

echo ""
echo "配置完成！服务状态："
echo "- Nginx: $(systemctl is-active nginx)"
echo "- SSL证书: $(if [ -f /etc/letsencrypt/live/$DOMAIN/fullchain.pem ]; then echo '已安装'; else echo '未安装'; fi)"
