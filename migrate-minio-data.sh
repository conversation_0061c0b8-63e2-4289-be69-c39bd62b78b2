#!/bin/bash

# MinIO数据迁移脚本
# 从远程MinIO实例(*************:9000)迁移数据到本地MinIO实例(localhost:9000)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
REMOTE_ALIAS="remote"
LOCAL_ALIAS="local"
BUCKET_NAME="product-images"
LOG_FILE="migration-$(date +%Y%m%d_%H%M%S).log"

# 创建日志文件
touch "$LOG_FILE"

log_info "开始MinIO数据迁移..."
log_info "日志文件: $LOG_FILE"

# 检查连接
log_info "检查远程MinIO连接..."
if ! mc ls $REMOTE_ALIAS > /dev/null 2>&1; then
    log_error "无法连接到远程MinIO实例"
    exit 1
fi
log_success "远程MinIO连接正常"

log_info "检查本地MinIO连接..."
if ! mc ls $LOCAL_ALIAS > /dev/null 2>&1; then
    log_error "无法连接到本地MinIO实例"
    exit 1
fi
log_success "本地MinIO连接正常"

# 检查远程存储桶是否存在
log_info "检查远程存储桶 $BUCKET_NAME..."
if ! mc ls $REMOTE_ALIAS/$BUCKET_NAME > /dev/null 2>&1; then
    log_error "远程存储桶 $BUCKET_NAME 不存在"
    exit 1
fi
log_success "远程存储桶 $BUCKET_NAME 存在"

# 检查本地存储桶，如果不存在则创建
log_info "检查本地存储桶 $BUCKET_NAME..."
if ! mc ls $LOCAL_ALIAS/$BUCKET_NAME > /dev/null 2>&1; then
    log_warning "本地存储桶 $BUCKET_NAME 不存在，正在创建..."
    mc mb $LOCAL_ALIAS/$BUCKET_NAME
    log_success "本地存储桶 $BUCKET_NAME 创建成功"
else
    log_success "本地存储桶 $BUCKET_NAME 已存在"
fi

# 获取远程数据统计
log_info "获取远程数据统计..."
REMOTE_STATS=$(mc du $REMOTE_ALIAS/$BUCKET_NAME)
log_info "远程数据统计: $REMOTE_STATS"

# 开始数据同步
log_info "开始同步数据..."
log_info "这可能需要一些时间，请耐心等待..."

# 使用mc mirror进行同步，保持目录结构
if mc mirror $REMOTE_ALIAS/$BUCKET_NAME $LOCAL_ALIAS/$BUCKET_NAME --overwrite --remove 2>&1 | tee -a "$LOG_FILE"; then
    log_success "数据同步完成"
else
    log_error "数据同步失败，请检查日志文件: $LOG_FILE"
    exit 1
fi

# 验证同步结果
log_info "验证同步结果..."
LOCAL_STATS=$(mc du $LOCAL_ALIAS/$BUCKET_NAME)
log_info "本地数据统计: $LOCAL_STATS"

# 比较文件数量
REMOTE_COUNT=$(echo "$REMOTE_STATS" | awk '{print $2}')
LOCAL_COUNT=$(echo "$LOCAL_STATS" | awk '{print $2}')

if [ "$REMOTE_COUNT" = "$LOCAL_COUNT" ]; then
    log_success "文件数量验证通过: $LOCAL_COUNT 个文件"
else
    log_warning "文件数量不匹配 - 远程: $REMOTE_COUNT, 本地: $LOCAL_COUNT"
fi

# 设置存储桶策略（公开读取）
log_info "设置存储桶访问策略..."
mc anonymous set public $LOCAL_ALIAS/$BUCKET_NAME
log_success "存储桶访问策略设置完成"

log_success "MinIO数据迁移完成！"
log_info "迁移日志保存在: $LOG_FILE"
