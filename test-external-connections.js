#!/usr/bin/env node

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

async function testConnections() {
  console.log('🔍 测试MongoDB和MinIO外部连接配置...\n');
  
  const serverIP = '************';
  
  // 测试MongoDB外部连接
  console.log('📊 测试MongoDB外部连接:');
  try {
    const { stdout } = await execAsync(`mongo --host ${serverIP}:27017 --eval "db.runCommand('ping')" --quiet`);
    console.log('✅ MongoDB外部连接成功');
  } catch (error) {
    console.log('❌ MongoDB外部连接失败:', error.message);
  }
  
  // 测试MinIO外部连接
  console.log('\n🗄️  测试MinIO外部连接:');
  try {
    const { stdout } = await execAsync(`curl -s -I http://${serverIP}:9000/minio/health/live`);
    if (stdout.includes('200 OK')) {
      console.log('✅ MinIO API外部连接成功');
    } else {
      console.log('⚠️  MinIO API响应异常');
    }
  } catch (error) {
    console.log('❌ MinIO API外部连接失败:', error.message);
  }
  
  // 测试MinIO控制台外部连接
  console.log('\n🖥️  测试MinIO控制台外部连接:');
  try {
    const { stdout } = await execAsync(`curl -s -I http://${serverIP}:9001`);
    if (stdout.includes('200') || stdout.includes('302')) {
      console.log('✅ MinIO控制台外部连接成功');
    } else {
      console.log('⚠️  MinIO控制台响应异常');
    }
  } catch (error) {
    console.log('❌ MinIO控制台外部连接失败:', error.message);
  }
  
  // 显示端口监听状态
  console.log('\n🔌 当前端口监听状态:');
  try {
    const { stdout } = await execAsync('ss -tlnp | grep -E "(27017|9000|9001)"');
    console.log(stdout);
  } catch (error) {
    console.log('无法获取端口状态');
  }
  
  console.log('\n📋 外部访问信息:');
  console.log(`MongoDB: mongodb://用户名:密码@${serverIP}:27017/数据库名?authSource=admin`);
  console.log(`MinIO API: http://${serverIP}:9000`);
  console.log(`MinIO控制台: http://${serverIP}:9001`);
  console.log(`MinIO访问密钥: lcsm`);
  console.log(`MinIO密钥: Sa2482047260`);
}

testConnections().catch(console.error);
