# HTTPS和域名配置指南

## 概述

本指南将帮助您为项目配置HTTPS和域名访问。您的域名 `rx.lout.me` 将通过Nginx反向代理访问PM2管理的应用。

## 当前服务状态

- **后端**: `products-backend` (端口3000, 集群模式2个实例)
- **前端**: `product-showcase-dev` (端口5173, 开发模式)
- **域名**: `rx.lout.me` (已解析到本机IP)

## 配置方案

### 方案一：一键配置（推荐）

运行完整配置脚本：

```bash
sudo ./setup-https.sh
```

此脚本将：
1. 安装Nginx
2. 配置反向代理
3. 获取SSL证书
4. 设置自动续期
5. 配置防火墙

### 方案二：分步配置

如果您希望分步骤进行配置：

#### 步骤1：配置Nginx
```bash
sudo ./nginx-config-only.sh
```

#### 步骤2：获取SSL证书
```bash
sudo ./setup-ssl.sh
```

## 配置详情

### Nginx配置说明

- **HTTP (端口80)**: 自动重定向到HTTPS，保留Let's Encrypt验证路径
- **HTTPS (端口443)**: 主要服务端口
- **前端代理**: `/` → `http://127.0.0.1:5173`
- **API代理**: `/api/` → `http://127.0.0.1:3000/api/`

### SSL证书

- 使用Let's Encrypt免费证书
- 自动续期（每天12:00检查）
- 支持TLS 1.2和1.3

### 安全配置

- HSTS (HTTP Strict Transport Security)
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- X-XSS-Protection
- Referrer-Policy

## 访问地址

配置完成后，您可以通过以下地址访问：

- **主站**: https://rx.lout.me
- **API**: https://rx.lout.me/api/
- **HTTP自动重定向**: http://rx.lout.me → https://rx.lout.me

## 管理和维护

### 使用管理脚本

```bash
# 查看服务状态
./https-manager.sh status

# 重启所有服务
./https-manager.sh restart

# 续期SSL证书
./https-manager.sh renew

# 测试证书续期
./https-manager.sh test

# 查看日志
./https-manager.sh logs

# 备份配置
./https-manager.sh backup
```

### 手动命令

```bash
# 查看PM2状态
pm2 list

# 查看Nginx状态
systemctl status nginx

# 查看SSL证书
certbot certificates

# 测试Nginx配置
nginx -t

# 重载Nginx配置
systemctl reload nginx
```

## 故障排除

### 常见问题

1. **域名解析问题**
   ```bash
   nslookup rx.lout.me
   dig rx.lout.me
   ```

2. **端口占用检查**
   ```bash
   lsof -i:80
   lsof -i:443
   lsof -i:3000
   lsof -i:5173
   ```

3. **防火墙设置**
   ```bash
   ufw status
   ufw allow 'Nginx Full'
   ```

4. **SSL证书问题**
   ```bash
   certbot certificates
   certbot renew --dry-run
   ```

### 日志位置

- **Nginx访问日志**: `/var/log/nginx/access.log`
- **Nginx错误日志**: `/var/log/nginx/error.log`
- **PM2日志**: `./products-backend/logs/` 和 `./product-showcase/logs/`
- **SSL证书日志**: `/var/log/letsencrypt/letsencrypt.log`

## 注意事项

1. **邮箱配置**: 请在脚本中将 `<EMAIL>` 替换为您的真实邮箱
2. **域名解析**: 确保域名已正确解析到服务器IP
3. **防火墙**: 确保端口80和443已开放
4. **PM2服务**: 确保PM2服务正常运行
5. **证书续期**: 系统会自动续期，无需手动干预

## 配置文件位置

- **Nginx配置**: `/etc/nginx/sites-available/rx.lout.me`
- **SSL证书**: `/etc/letsencrypt/live/rx.lout.me/`
- **PM2配置**: `./products-backend/ecosystem.config.js` 和 `./product-showcase/ecosystem.config.cjs`

配置完成后，您的网站将通过HTTPS安全访问，所有HTTP请求会自动重定向到HTTPS。
