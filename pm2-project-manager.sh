#!/bin/bash

# 项目整体PM2管理脚本
# 用法: ./pm2-project-manager.sh [command]

BACKEND_DIR="products-backend"
FRONTEND_DIR="product-showcase"
PROJECT_ROOT="/root/products-b-test"

case "$1" in
    start-all)
        echo "启动所有服务..."
        echo "1. 启动后端服务..."
        cd $PROJECT_ROOT/$BACKEND_DIR
        pm2 start ecosystem.config.js
        echo "2. 启动前端开发服务器..."
        cd $PROJECT_ROOT/$FRONTEND_DIR
        pm2 start ecosystem.config.cjs --only product-showcase-dev
        echo "所有服务启动完成！"
        ;;
    stop-all)
        echo "停止所有服务..."
        pm2 stop all
        ;;
    restart-all)
        echo "重启所有服务..."
        pm2 restart all
        ;;
    status)
        echo "查看所有服务状态..."
        pm2 status
        ;;
    logs)
        echo "查看所有服务日志..."
        pm2 logs
        ;;
    monit)
        echo "打开监控界面..."
        pm2 monit
        ;;
    backend)
        echo "管理后端服务..."
        cd $PROJECT_ROOT/$BACKEND_DIR
        if [ -z "$2" ]; then
            echo "用法: $0 backend [start|stop|restart|logs|status]"
            exit 1
        fi
        ./pm2-manage.sh $2
        ;;
    frontend)
        echo "管理前端服务..."
        cd $PROJECT_ROOT/$FRONTEND_DIR
        if [ -z "$2" ]; then
            echo "用法: $0 frontend [dev|preview|stop-dev|stop-preview|logs-dev|logs-preview]"
            exit 1
        fi
        ./pm2-manage.sh $2
        ;;
    build-all)
        echo "构建所有项目..."
        echo "1. 构建后端..."
        cd $PROJECT_ROOT/$BACKEND_DIR
        npm run build
        echo "2. 构建前端..."
        cd $PROJECT_ROOT/$FRONTEND_DIR
        npm run build
        echo "构建完成！"
        ;;
    deploy)
        echo "部署所有服务..."
        echo "1. 构建项目..."
        $0 build-all
        if [ $? -ne 0 ]; then
            echo "构建失败，停止部署"
            exit 1
        fi
        echo "2. 重启后端服务..."
        cd $PROJECT_ROOT/$BACKEND_DIR
        pm2 restart products-backend
        echo "3. 启动前端预览服务器..."
        cd $PROJECT_ROOT/$FRONTEND_DIR
        pm2 start ecosystem.config.cjs --only product-showcase-preview
        echo "部署完成！"
        ;;
    save)
        echo "保存PM2进程列表..."
        pm2 save
        ;;
    health-check)
        echo "执行健康检查..."
        echo "1. 检查后端健康状态..."
        curl -s http://localhost:3000/health | head -1
        echo ""
        echo "2. 检查前端服务状态..."
        curl -s -I http://localhost:5173 | head -1
        echo ""
        echo "3. PM2进程状态..."
        pm2 status
        ;;
    *)
        echo "项目PM2管理脚本"
        echo "用法: $0 {start-all|stop-all|restart-all|status|logs|monit|backend|frontend|build-all|deploy|save|health-check}"
        echo ""
        echo "全局命令:"
        echo "  start-all      - 启动所有服务（后端+前端开发服务器）"
        echo "  stop-all       - 停止所有服务"
        echo "  restart-all    - 重启所有服务"
        echo "  status         - 查看所有服务状态"
        echo "  logs           - 查看所有服务日志"
        echo "  monit          - 打开监控界面"
        echo "  build-all      - 构建所有项目"
        echo "  deploy         - 部署所有服务（构建+重启）"
        echo "  save           - 保存PM2进程列表"
        echo "  health-check   - 执行健康检查"
        echo ""
        echo "子服务管理:"
        echo "  backend [cmd]  - 管理后端服务"
        echo "    可用命令: start|stop|restart|reload|logs|status|build-restart"
        echo "  frontend [cmd] - 管理前端服务"
        echo "    可用命令: dev|preview|stop-dev|stop-preview|logs-dev|logs-preview"
        echo ""
        echo "服务访问地址:"
        echo "  后端API: http://localhost:3000"
        echo "  前端开发: http://localhost:5173"
        echo "  前端预览: http://localhost:4173"
        echo ""
        echo "示例:"
        echo "  $0 start-all           # 启动所有服务"
        echo "  $0 backend restart     # 重启后端服务"
        echo "  $0 frontend dev        # 启动前端开发服务器"
        echo "  $0 health-check       # 检查服务健康状态"
        exit 1
        ;;
esac
